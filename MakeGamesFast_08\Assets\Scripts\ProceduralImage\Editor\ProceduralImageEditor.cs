using UnityEngine;
using UnityEditor;
using UnityEditor.UI;

[CustomEditor(typeof(ProceduralImage), true)]
[CanEditMultipleObjects]
public class ProceduralImageEditor : GraphicEditor
{
    public override void OnInspectorGUI()
    {
        ProceduralImage proceduralImage = (ProceduralImage)target;

        EditorGUI.BeginChangeCheck();

        // Shape section
        EditorGUILayout.LabelField("Shape", EditorStyles.boldLabel);
        float newCornerRadius = EditorGUILayout.FloatField("Corner Radius", proceduralImage.cornerRadius);

        EditorGUILayout.Space();

        // Fill section
        EditorGUILayout.LabelField("Fill", EditorStyles.boldLabel);
        Color newFillColor = EditorGUILayout.ColorField("Fill Color", proceduralImage.fillColor);
        Texture2D newFillTexture = (Texture2D)EditorGUILayout.ObjectField("Fill Texture", proceduralImage.fillTexture, typeof(Texture2D), false);
        Color newTintColor = EditorGUILayout.ColorField("Tint Color", proceduralImage.tintColor);

        EditorGUILayout.Space();

        // Stroke section
        EditorGUILayout.LabelField("Stroke", EditorStyles.boldLabel);
        StrokeLocation newStrokeLocation = (StrokeLocation)EditorGUILayout.EnumPopup("Stroke Location", proceduralImage.strokeLocation);
        float newStrokeWidth = EditorGUILayout.FloatField("Stroke Width", proceduralImage.strokeWidth);
        Color newStrokeColor = EditorGUILayout.ColorField("Stroke Color", proceduralImage.strokeColor);

        EditorGUILayout.Space();

        // Image section
        EditorGUILayout.LabelField("Image", EditorStyles.boldLabel);
        Texture2D newImage = (Texture2D)EditorGUILayout.ObjectField("Image", proceduralImage.image, typeof(Texture2D), false);
        Color newImageColor = EditorGUILayout.ColorField("Image Color", proceduralImage.imageColor);

        if (EditorGUI.EndChangeCheck())
        {
            Undo.RecordObject(proceduralImage, "Procedural Image Properties");

            proceduralImage.cornerRadius = newCornerRadius;
            proceduralImage.fillColor = newFillColor;
            proceduralImage.fillTexture = newFillTexture;
            proceduralImage.tintColor = newTintColor;
            proceduralImage.strokeLocation = newStrokeLocation;
            proceduralImage.strokeWidth = newStrokeWidth;
            proceduralImage.strokeColor = newStrokeColor;
            proceduralImage.image = newImage;
            proceduralImage.imageColor = newImageColor;

            EditorUtility.SetDirty(proceduralImage);
        }

        EditorGUILayout.Space();

        // Base Graphic properties
        base.OnInspectorGUI();
    }
}
