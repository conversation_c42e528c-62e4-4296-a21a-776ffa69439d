using UnityEngine;
using UnityEditor;
using UnityEditor.UI;

[CustomEditor(typeof(ProceduralImage), true)]
[CanEditMultipleObjects]
public class ProceduralImageEditor : GraphicEditor
{
    SerializedProperty m_CornerRadius;
    SerializedProperty m_FillColor;
    SerializedProperty m_FillTexture;
    SerializedProperty m_TintColor;
    SerializedProperty m_StrokeLocation;
    SerializedProperty m_StrokeWidth;
    SerializedProperty m_StrokeColor;
    SerializedProperty m_Image;
    SerializedProperty m_ImageColor;

    protected override void OnEnable()
    {
        base.OnEnable();
        
        m_CornerRadius = serializedObject.FindProperty("_cornerRadius");
        m_FillColor = serializedObject.FindProperty("_fillColor");
        m_FillTexture = serializedObject.FindProperty("_fillTexture");
        m_TintColor = serializedObject.FindProperty("_tintColor");
        m_StrokeLocation = serializedObject.FindProperty("_strokeLocation");
        m_StrokeWidth = serializedObject.FindProperty("_strokeWidth");
        m_StrokeColor = serializedObject.FindProperty("_strokeColor");
        m_Image = serializedObject.FindProperty("_image");
        m_ImageColor = serializedObject.FindProperty("_imageColor");
    }

    public override void OnInspectorGUI()
    {
        serializedObject.Update();

        // Shape section
        EditorGUILayout.LabelField("Shape", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(m_CornerRadius, new GUIContent("Corner Radius"));

        EditorGUILayout.Space();

        // Fill section
        EditorGUILayout.LabelField("Fill", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(m_FillColor, new GUIContent("Fill Color"));
        EditorGUILayout.PropertyField(m_FillTexture, new GUIContent("Fill Texture"));
        EditorGUILayout.PropertyField(m_TintColor, new GUIContent("Tint Color"));

        EditorGUILayout.Space();

        // Stroke section
        EditorGUILayout.LabelField("Stroke", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(m_StrokeLocation, new GUIContent("Stroke Location"));
        EditorGUILayout.PropertyField(m_StrokeWidth, new GUIContent("Stroke Width"));
        EditorGUILayout.PropertyField(m_StrokeColor, new GUIContent("Stroke Color"));

        EditorGUILayout.Space();

        // Image section
        EditorGUILayout.LabelField("Image", EditorStyles.boldLabel);
        EditorGUILayout.PropertyField(m_Image, new GUIContent("Image"));
        EditorGUILayout.PropertyField(m_ImageColor, new GUIContent("Image Color"));

        EditorGUILayout.Space();

        // Base Graphic properties
        base.OnInspectorGUI();

        serializedObject.ApplyModifiedProperties();
    }
}
