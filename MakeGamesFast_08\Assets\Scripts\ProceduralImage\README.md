# Procedural Image Component

A Unity UI component that creates procedural rounded rectangles compatible with Unity's Canvas system, including support for masking and layout systems.

## Features

- **Rounded Corners**: Adjustable corner radius with automatic clamping to prevent invalid shapes
- **Fill System**: Solid color fill with optional texture and tint color support
- **Stroke System**: Configurable stroke with inside, outside, or center positioning
- **Image Support**: Separate image texture that can be overlaid on the fill
- **Unity Integration**: Full compatibility with Canvas rendering, masking, and layout systems
- **Performance**: Efficient mesh generation using Unity's VertexHelper system

## Usage

### Basic Setup

1. Create a UI GameObject (GameObject > UI > Image)
2. Remove the Image component
3. Add the ProceduralImage component
4. Configure the properties in the inspector

### Properties

#### Shape
- **Corner Radius**: Controls the roundness of corners (0 = rectangle, higher values = more rounded)

#### Fill
- **Fill Color**: Base color of the shape
- **Fill Texture**: Optional texture to apply to the fill
- **Tint Color**: Color multiplied with the fill texture

#### Stroke
- **Stroke Location**: Where the stroke is positioned relative to the shape boundary
  - `Center`: Stroke centered on the boundary
  - `Inside`: Stroke drawn inside the shape
  - `Outside`: Stroke drawn outside the shape
- **Stroke Width**: Thickness of the stroke (0 = no stroke)
- **Stroke Color**: Color of the stroke

#### Image
- **Image**: Optional image texture overlaid on the shape
- **Image Color**: Tint color for the image texture

### Code Examples

```csharp
// Get reference to ProceduralImage
ProceduralImage procImg = GetComponent<ProceduralImage>();

// Set basic properties
procImg.cornerRadius = 15f;
procImg.fillColor = Color.blue;
procImg.strokeWidth = 2f;
procImg.strokeColor = Color.white;
procImg.strokeLocation = StrokeLocation.Inside;

// Apply textures
procImg.fillTexture = myTexture;
procImg.tintColor = Color.red; // Tints the fill texture

// Add an overlay image
procImg.image = overlayTexture;
procImg.imageColor = Color.white;
```

### Runtime Animation

```csharp
// Animate corner radius
StartCoroutine(AnimateRadius());

IEnumerator AnimateRadius()
{
    float duration = 2f;
    float startRadius = 0f;
    float endRadius = 25f;
    
    for (float t = 0; t < duration; t += Time.deltaTime)
    {
        float progress = t / duration;
        procImg.cornerRadius = Mathf.Lerp(startRadius, endRadius, progress);
        yield return null;
    }
}
```

## Technical Details

### Inheritance
- Inherits from `MaskableGraphic` for full Unity UI integration
- Supports all standard Graphic features (color, material, raycast target, etc.)

### Mesh Generation
- Uses Unity's `VertexHelper` for efficient mesh creation
- Generates triangulated meshes for rounded corners
- Optimizes vertex count based on corner radius

### Performance Considerations
- Corner segments are automatically calculated based on radius
- Larger corner radius = more vertices for smoother curves
- Stroke rendering uses simple quad-based approach for performance

### Limitations
- Rounded rectangle stroke implementation is simplified (falls back to rectangle stroke)
- Complex stroke shapes with rounded corners require additional development
- UV mapping is basic and may not suit all texture requirements

## Integration with Unity Systems

### Layout System
- Fully compatible with Unity's layout system
- Responds to RectTransform dimension changes
- Works with ContentSizeFitter, LayoutGroup, etc.

### Masking
- Supports Unity's built-in masking system
- Works with Mask and RectMask2D components
- Properly clips content within masked areas

### Canvas Rendering
- Integrates with Canvas batching system
- Supports different Canvas render modes
- Compatible with Canvas scaling

## Demo Scene

Use the `ProceduralImageDemo` component to create interactive demonstrations:

1. Add ProceduralImageDemo to a GameObject with ProceduralImage
2. Assign UI controls (sliders, toggles, dropdowns) to the demo script
3. Use the provided methods for runtime manipulation and animation

## Future Enhancements

- Advanced stroke rendering for rounded rectangles
- More sophisticated UV mapping options
- Additional shape types (circles, polygons)
- Gradient fill support
- Shadow and glow effects
