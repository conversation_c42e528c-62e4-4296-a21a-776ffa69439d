using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Simple test script to create and configure a ProceduralImage component
/// </summary>
public class ProceduralImageTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool createOnStart = true;
    [SerializeField] private Vector2 imageSize = new Vector2(200, 100);
    [SerializeField] private float testCornerRadius = 25f;
    [SerializeField] private Color testFillColor = Color.blue;
    [SerializeField] private float testStrokeWidth = 5f;
    [SerializeField] private Color testStrokeColor = Color.yellow;

    private void Start()
    {
        if (createOnStart)
        {
            CreateTestProceduralImage();
        }
    }

    [ContextMenu("Create Test Procedural Image")]
    public void CreateTestProceduralImage()
    {
        // Find or create Canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }

        // Create GameObject with ProceduralImage
        GameObject imageGO = new GameObject("Test Procedural Image");
        imageGO.transform.SetParent(canvas.transform, false);

        // Add RectTransform and configure
        RectTransform rectTransform = imageGO.AddComponent<RectTransform>();
        rectTransform.sizeDelta = imageSize;
        rectTransform.anchoredPosition = Vector2.zero;

        // Add ProceduralImage component
        ProceduralImage procImg = imageGO.AddComponent<ProceduralImage>();

        // Configure the component
        procImg.cornerRadius = testCornerRadius;
        procImg.fillColor = testFillColor;
        procImg.strokeWidth = testStrokeWidth;
        procImg.strokeColor = testStrokeColor;
        procImg.strokeLocation = StrokeLocation.Inside;

        Debug.Log("Created test ProceduralImage with the following settings:");
        Debug.Log($"Size: {imageSize}, Corner Radius: {testCornerRadius}");
        Debug.Log($"Fill Color: {testFillColor}, Stroke: {testStrokeWidth}px {testStrokeColor}");
    }

    [ContextMenu("Test Property Changes")]
    public void TestPropertyChanges()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Test changing properties
        procImg.cornerRadius = Random.Range(0f, 25f);
        procImg.fillColor = new Color(Random.value, Random.value, Random.value, 1f);
        procImg.strokeWidth = Random.Range(0f, 10f);
        procImg.strokeColor = new Color(Random.value, Random.value, Random.value, 1f);

        Debug.Log("Changed ProceduralImage properties randomly");
    }

    [ContextMenu("Test Stroke Locations")]
    public void TestStrokeLocations()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Cycle through stroke locations
        switch (procImg.strokeLocation)
        {
            case StrokeLocation.Center:
                procImg.strokeLocation = StrokeLocation.Inside;
                Debug.Log("Stroke location changed to Inside");
                break;
            case StrokeLocation.Inside:
                procImg.strokeLocation = StrokeLocation.Outside;
                Debug.Log("Stroke location changed to Outside");
                break;
            case StrokeLocation.Outside:
                procImg.strokeLocation = StrokeLocation.Center;
                Debug.Log("Stroke location changed to Center");
                break;
        }
    }

    [ContextMenu("Test Corner Radius Animation")]
    public void TestCornerRadiusAnimation()
    {
        StartCoroutine(AnimateCornerRadius());
    }

    private System.Collections.IEnumerator AnimateCornerRadius()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null) yield break;

        float duration = 3f;
        float maxRadius = 50f;

        for (float t = 0; t < duration; t += Time.deltaTime)
        {
            float progress = t / duration;
            float radius = Mathf.Sin(progress * Mathf.PI * 2) * maxRadius * 0.5f + maxRadius * 0.5f;
            procImg.cornerRadius = radius;
            yield return null;
        }

        procImg.cornerRadius = testCornerRadius;
    }

    [ContextMenu("Test Edge Cases")]
    public void TestEdgeCases()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Test case 1: Small corner radius with large stroke
        Debug.Log("Testing: Small corner radius (5) with large stroke (20)");
        procImg.cornerRadius = 5f;
        procImg.strokeWidth = 20f;
        procImg.strokeLocation = StrokeLocation.Inside;

        // Wait a moment then test next case
        StartCoroutine(TestEdgeCasesSequence(procImg));
    }

    private System.Collections.IEnumerator TestEdgeCasesSequence(ProceduralImage procImg)
    {
        yield return new WaitForSeconds(2f);

        // Test case 2: Zero corner radius with large stroke
        Debug.Log("Testing: Zero corner radius with large stroke (15)");
        procImg.cornerRadius = 0f;
        procImg.strokeWidth = 15f;
        procImg.strokeLocation = StrokeLocation.Center;

        yield return new WaitForSeconds(2f);

        // Test case 3: Large corner radius with large stroke
        Debug.Log("Testing: Large corner radius (30) with large stroke (25)");
        procImg.cornerRadius = 30f;
        procImg.strokeWidth = 25f;
        procImg.strokeLocation = StrokeLocation.Outside;

        yield return new WaitForSeconds(2f);

        // Test case 4: Very small shape with stroke
        Debug.Log("Testing: Very small shape (50x30) with stroke");
        RectTransform rt = procImg.GetComponent<RectTransform>();
        Vector2 originalSize = rt.sizeDelta;
        rt.sizeDelta = new Vector2(50f, 30f);
        procImg.cornerRadius = 10f;
        procImg.strokeWidth = 8f;

        yield return new WaitForSeconds(2f);

        // Restore original settings
        Debug.Log("Restoring original settings");
        rt.sizeDelta = originalSize;
        procImg.cornerRadius = testCornerRadius;
        procImg.strokeWidth = testStrokeWidth;
        procImg.strokeLocation = StrokeLocation.Inside;
    }
}
