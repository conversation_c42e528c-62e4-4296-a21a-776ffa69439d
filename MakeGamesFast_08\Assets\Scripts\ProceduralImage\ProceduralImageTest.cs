using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Simple test script to create and configure a ProceduralImage component
/// </summary>
public class ProceduralImageTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool createOnStart = true;
    [SerializeField] private Vector2 imageSize = new Vector2(200, 100);
    [SerializeField] private float testCornerRadius = 25f;
    [SerializeField] private Color testFillColor = Color.blue;
    [SerializeField] private float testStrokeWidth = 5f;
    [SerializeField] private Color testStrokeColor = Color.yellow;

    private void Start()
    {
        if (createOnStart)
        {
            CreateTestProceduralImage();
        }
    }

    [ContextMenu("Create Test Procedural Image")]
    public void CreateTestProceduralImage()
    {
        // Find or create Canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }

        // Create GameObject with ProceduralImage
        GameObject imageGO = new GameObject("Test Procedural Image");
        imageGO.transform.SetParent(canvas.transform, false);

        // Add RectTransform and configure
        RectTransform rectTransform = imageGO.AddComponent<RectTransform>();
        rectTransform.sizeDelta = imageSize;
        rectTransform.anchoredPosition = Vector2.zero;

        // Add ProceduralImage component
        ProceduralImage procImg = imageGO.AddComponent<ProceduralImage>();

        // Configure the component
        procImg.cornerRadius = testCornerRadius;
        procImg.fillColor = testFillColor;
        procImg.strokeWidth = testStrokeWidth;
        procImg.strokeColor = testStrokeColor;
        procImg.strokeLocation = StrokeLocation.Inside;

        Debug.Log("Created test ProceduralImage with the following settings:");
        Debug.Log($"Size: {imageSize}, Corner Radius: {testCornerRadius}");
        Debug.Log($"Fill Color: {testFillColor}, Stroke: {testStrokeWidth}px {testStrokeColor}");
    }

    [ContextMenu("Test Property Changes")]
    public void TestPropertyChanges()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Test changing properties
        procImg.cornerRadius = Random.Range(0f, 25f);
        procImg.fillColor = new Color(Random.value, Random.value, Random.value, 1f);
        procImg.strokeWidth = Random.Range(0f, 10f);
        procImg.strokeColor = new Color(Random.value, Random.value, Random.value, 1f);

        Debug.Log("Changed ProceduralImage properties randomly");
    }

    [ContextMenu("Test Stroke Locations")]
    public void TestStrokeLocations()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Cycle through stroke locations
        switch (procImg.strokeLocation)
        {
            case StrokeLocation.Center:
                procImg.strokeLocation = StrokeLocation.Inside;
                Debug.Log("Stroke location changed to Inside");
                break;
            case StrokeLocation.Inside:
                procImg.strokeLocation = StrokeLocation.Outside;
                Debug.Log("Stroke location changed to Outside");
                break;
            case StrokeLocation.Outside:
                procImg.strokeLocation = StrokeLocation.Center;
                Debug.Log("Stroke location changed to Center");
                break;
        }
    }

    [ContextMenu("Test Corner Radius Animation")]
    public void TestCornerRadiusAnimation()
    {
        StartCoroutine(AnimateCornerRadius());
    }

    private System.Collections.IEnumerator AnimateCornerRadius()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null) yield break;

        float duration = 3f;
        float maxRadius = 50f;

        for (float t = 0; t < duration; t += Time.deltaTime)
        {
            float progress = t / duration;
            float radius = Mathf.Sin(progress * Mathf.PI * 2) * maxRadius * 0.5f + maxRadius * 0.5f;
            procImg.cornerRadius = radius;
            yield return null;
        }

        procImg.cornerRadius = testCornerRadius;
    }
}
