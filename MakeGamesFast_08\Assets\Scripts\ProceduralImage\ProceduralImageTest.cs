using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Simple test script to create and configure a ProceduralImage component
/// </summary>
public class ProceduralImageTest : MonoBehaviour
{
    [Header("Test Configuration")]
    [SerializeField] private bool createOnStart = true;
    [SerializeField] private Vector2 imageSize = new Vector2(200, 100);
    [SerializeField] private float testCornerRadius = 25f;
    [SerializeField] private Color testFillColor = Color.blue;
    [SerializeField] private float testStrokeWidth = 5f;
    [SerializeField] private Color testStrokeColor = Color.yellow;

    private void Start()
    {
        if (createOnStart)
        {
            CreateTestProceduralImage();
        }
    }

    [ContextMenu("Create Test Procedural Image")]
    public void CreateTestProceduralImage()
    {
        // Find or create Canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }

        // Create GameObject with ProceduralImage
        GameObject imageGO = new GameObject("Test Procedural Image");
        imageGO.transform.SetParent(canvas.transform, false);

        // Add RectTransform and configure
        RectTransform rectTransform = imageGO.AddComponent<RectTransform>();
        rectTransform.sizeDelta = imageSize;
        rectTransform.anchoredPosition = Vector2.zero;

        // Add ProceduralImage component
        ProceduralImage procImg = imageGO.AddComponent<ProceduralImage>();

        // Configure the component
        procImg.cornerRadius = testCornerRadius;
        procImg.fillColor = testFillColor;
        procImg.strokeWidth = testStrokeWidth;
        procImg.strokeColor = testStrokeColor;
        procImg.strokeLocation = StrokeLocation.Inside;

        Debug.Log("Created test ProceduralImage with the following settings:");
        Debug.Log($"Size: {imageSize}, Corner Radius: {testCornerRadius}");
        Debug.Log($"Fill Color: {testFillColor}, Stroke: {testStrokeWidth}px {testStrokeColor}");
    }

    [ContextMenu("Test Property Changes")]
    public void TestPropertyChanges()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Test changing properties
        procImg.cornerRadius = Random.Range(0f, 25f);
        procImg.fillColor = new Color(Random.value, Random.value, Random.value, 1f);
        procImg.strokeWidth = Random.Range(0f, 10f);
        procImg.strokeColor = new Color(Random.value, Random.value, Random.value, 1f);

        Debug.Log("Changed ProceduralImage properties randomly");
    }

    [ContextMenu("Test Stroke Locations")]
    public void TestStrokeLocations()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Cycle through stroke locations
        switch (procImg.strokeLocation)
        {
            case StrokeLocation.Center:
                procImg.strokeLocation = StrokeLocation.Inside;
                Debug.Log("Stroke location changed to Inside");
                break;
            case StrokeLocation.Inside:
                procImg.strokeLocation = StrokeLocation.Outside;
                Debug.Log("Stroke location changed to Outside");
                break;
            case StrokeLocation.Outside:
                procImg.strokeLocation = StrokeLocation.Center;
                Debug.Log("Stroke location changed to Center");
                break;
        }
    }

    [ContextMenu("Test Corner Radius Animation")]
    public void TestCornerRadiusAnimation()
    {
        StartCoroutine(AnimateCornerRadius());
    }

    private System.Collections.IEnumerator AnimateCornerRadius()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null) yield break;

        float duration = 3f;
        float maxRadius = 50f;

        for (float t = 0; t < duration; t += Time.deltaTime)
        {
            float progress = t / duration;
            float radius = Mathf.Sin(progress * Mathf.PI * 2) * maxRadius * 0.5f + maxRadius * 0.5f;
            procImg.cornerRadius = radius;
            yield return null;
        }

        procImg.cornerRadius = testCornerRadius;
    }

    [ContextMenu("Test Edge Cases")]
    public void TestEdgeCases()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        // Test case 1: Small corner radius with large stroke
        Debug.Log("Testing: Small corner radius (5) with large stroke (20)");
        procImg.cornerRadius = 5f;
        procImg.strokeWidth = 20f;
        procImg.strokeLocation = StrokeLocation.Inside;

        // Wait a moment then test next case
        StartCoroutine(TestEdgeCasesSequence(procImg));
    }

    private System.Collections.IEnumerator TestEdgeCasesSequence(ProceduralImage procImg)
    {
        yield return new WaitForSeconds(2f);

        // Test case 2: Zero corner radius with large stroke
        Debug.Log("Testing: Zero corner radius with large stroke (15)");
        procImg.cornerRadius = 0f;
        procImg.strokeWidth = 15f;
        procImg.strokeLocation = StrokeLocation.Center;

        yield return new WaitForSeconds(2f);

        // Test case 3: Large corner radius with large stroke
        Debug.Log("Testing: Large corner radius (30) with large stroke (25)");
        procImg.cornerRadius = 30f;
        procImg.strokeWidth = 25f;
        procImg.strokeLocation = StrokeLocation.Outside;

        yield return new WaitForSeconds(2f);

        // Test case 4: Very small shape with stroke
        Debug.Log("Testing: Very small shape (50x30) with stroke");
        RectTransform rt = procImg.GetComponent<RectTransform>();
        Vector2 originalSize = rt.sizeDelta;
        rt.sizeDelta = new Vector2(50f, 30f);
        procImg.cornerRadius = 10f;
        procImg.strokeWidth = 8f;

        yield return new WaitForSeconds(2f);

        // Restore original settings
        Debug.Log("Restoring original settings");
        rt.sizeDelta = originalSize;
        procImg.cornerRadius = testCornerRadius;
        procImg.strokeWidth = testStrokeWidth;
        procImg.strokeLocation = StrokeLocation.Inside;
    }

    [ContextMenu("Test Extreme Case (80x120, r=0.5, stroke=15)")]
    public void TestExtremeCase()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        RectTransform rt = procImg.GetComponent<RectTransform>();
        Vector2 originalSize = rt.sizeDelta;
        float originalCornerRadius = procImg.cornerRadius;
        float originalStrokeWidth = procImg.strokeWidth;
        StrokeLocation originalStrokeLocation = procImg.strokeLocation;

        Debug.Log("Testing extreme case: 80x120, corner radius 0.5, stroke width 15");

        // Set extreme values
        rt.sizeDelta = new Vector2(80f, 120f);
        procImg.cornerRadius = 0.5f;
        procImg.strokeWidth = 15f;
        procImg.strokeLocation = StrokeLocation.Inside;
        procImg.fillColor = Color.blue;
        procImg.strokeColor = Color.red;

        StartCoroutine(TestExtremeSequence(procImg, rt, originalSize, originalCornerRadius, originalStrokeWidth, originalStrokeLocation));
    }

    private System.Collections.IEnumerator TestExtremeSequence(ProceduralImage procImg, RectTransform rt, Vector2 originalSize, float originalCornerRadius, float originalStrokeWidth, StrokeLocation originalStrokeLocation)
    {
        yield return new WaitForSeconds(3f);

        Debug.Log("Testing with Center stroke location");
        procImg.strokeLocation = StrokeLocation.Center;

        yield return new WaitForSeconds(2f);

        Debug.Log("Testing with Outside stroke location");
        procImg.strokeLocation = StrokeLocation.Outside;

        yield return new WaitForSeconds(2f);

        Debug.Log("Testing with even larger stroke (25px)");
        procImg.strokeWidth = 25f;
        procImg.strokeLocation = StrokeLocation.Inside;

        yield return new WaitForSeconds(2f);

        Debug.Log("Restoring original settings");
        rt.sizeDelta = originalSize;
        procImg.cornerRadius = originalCornerRadius;
        procImg.strokeWidth = originalStrokeWidth;
        procImg.strokeLocation = originalStrokeLocation;
    }

    [ContextMenu("Test Corner Overlap Cases")]
    public void TestCornerOverlapCases()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        StartCoroutine(TestCornerOverlapSequence(procImg));
    }

    private System.Collections.IEnumerator TestCornerOverlapSequence(ProceduralImage procImg)
    {
        // Store original values
        float originalCornerRadius = procImg.cornerRadius;
        float originalStrokeWidth = procImg.strokeWidth;
        StrokeLocation originalStrokeLocation = procImg.strokeLocation;

        // Test case 1: Inside stroke where stroke width > 2 * corner radius
        Debug.Log("Test 1: Inside stroke, corner radius 5, stroke width 12 (stroke > 2*radius)");
        procImg.cornerRadius = 5f;
        procImg.strokeWidth = 12f;
        procImg.strokeLocation = StrokeLocation.Inside;
        procImg.fillColor = Color.blue;
        procImg.strokeColor = Color.red;

        yield return new WaitForSeconds(3f);

        // Test case 2: Center stroke where stroke width > corner radius
        Debug.Log("Test 2: Center stroke, corner radius 8, stroke width 10 (stroke > radius)");
        procImg.cornerRadius = 8f;
        procImg.strokeWidth = 10f;
        procImg.strokeLocation = StrokeLocation.Center;

        yield return new WaitForSeconds(3f);

        // Test case 3: Very small corner radius with large stroke
        Debug.Log("Test 3: Very small corner radius 1, stroke width 15");
        procImg.cornerRadius = 1f;
        procImg.strokeWidth = 15f;
        procImg.strokeLocation = StrokeLocation.Inside;

        yield return new WaitForSeconds(3f);

        // Test case 4: Zero corner radius with large stroke (should work fine)
        Debug.Log("Test 4: Zero corner radius, stroke width 15 (reference case)");
        procImg.cornerRadius = 0f;
        procImg.strokeWidth = 15f;

        yield return new WaitForSeconds(3f);

        // Restore original values
        Debug.Log("Restoring original settings");
        procImg.cornerRadius = originalCornerRadius;
        procImg.strokeWidth = originalStrokeWidth;
        procImg.strokeLocation = originalStrokeLocation;
    }

    [ContextMenu("Test Three Algorithm Cases")]
    public void TestThreeAlgorithmCases()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        StartCoroutine(TestAlgorithmSequence(procImg));
    }

    private System.Collections.IEnumerator TestAlgorithmSequence(ProceduralImage procImg)
    {
        // Store original values
        float originalCornerRadius = procImg.cornerRadius;
        float originalStrokeWidth = procImg.strokeWidth;
        StrokeLocation originalStrokeLocation = procImg.strokeLocation;

        // Algorithm 1: Normal stroke (inner stroke width < corner radius)
        Debug.Log("Algorithm 1: Normal stroke - Corner radius 20, Inside stroke 15 (15 < 20)");
        procImg.cornerRadius = 20f;
        procImg.strokeWidth = 15f;
        procImg.strokeLocation = StrokeLocation.Inside;
        procImg.fillColor = Color.blue;
        procImg.strokeColor = Color.red;

        yield return new WaitForSeconds(4f);

        // Algorithm 2: Simplified inner (inner stroke width >= corner radius)
        Debug.Log("Algorithm 2: Quarter-circles + rectangles - Corner radius 10, Inside stroke 15 (15 >= 10)");
        procImg.cornerRadius = 10f;
        procImg.strokeWidth = 15f;
        procImg.strokeLocation = StrokeLocation.Inside;

        yield return new WaitForSeconds(4f);

        // Algorithm 3: Fill shape (inner stroke width >= half min dimension)
        Debug.Log("Algorithm 3: Fill shape - Size 100x80, Inside stroke 45 (45 >= 40)");
        RectTransform rt = procImg.GetComponent<RectTransform>();
        Vector2 originalSize = rt.sizeDelta;
        rt.sizeDelta = new Vector2(100f, 80f); // min dimension = 80, half = 40
        procImg.cornerRadius = 15f;
        procImg.strokeWidth = 45f; // >= 40
        procImg.strokeLocation = StrokeLocation.Inside;

        yield return new WaitForSeconds(4f);

        // Test with Center stroke location
        Debug.Log("Testing Center stroke with same settings");
        procImg.strokeLocation = StrokeLocation.Center; // inner stroke width = 22.5, still >= 40/2

        yield return new WaitForSeconds(3f);

        // Test with Outside stroke location
        Debug.Log("Testing Outside stroke with same settings");
        procImg.strokeLocation = StrokeLocation.Outside; // inner stroke width = 0, should use Algorithm 1

        yield return new WaitForSeconds(3f);

        // Restore original values
        Debug.Log("Restoring original settings");
        rt.sizeDelta = originalSize;
        procImg.cornerRadius = originalCornerRadius;
        procImg.strokeWidth = originalStrokeWidth;
        procImg.strokeLocation = originalStrokeLocation;
    }

    [ContextMenu("Test Threshold Edge Cases")]
    public void TestThresholdEdgeCases()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        StartCoroutine(TestThresholdSequence(procImg));
    }

    private System.Collections.IEnumerator TestThresholdSequence(ProceduralImage procImg)
    {
        // Store original values
        float originalCornerRadius = procImg.cornerRadius;
        float originalStrokeWidth = procImg.strokeWidth;
        StrokeLocation originalStrokeLocation = procImg.strokeLocation;

        procImg.fillColor = Color.blue;
        procImg.strokeColor = Color.red;
        procImg.strokeLocation = StrokeLocation.Inside;

        // Test around the 85% threshold
        float cornerRadius = 20f;
        procImg.cornerRadius = cornerRadius;

        // Test 1: Well below threshold (should use normal stroke)
        float strokeWidth1 = cornerRadius * 0.7f; // 70% - well below 85%
        Debug.Log($"Test 1: Normal stroke - Corner radius {cornerRadius}, stroke {strokeWidth1:F1} ({strokeWidth1 / cornerRadius * 100:F0}% of radius)");
        procImg.strokeWidth = strokeWidth1;

        yield return new WaitForSeconds(3f);

        // Test 2: Just below threshold (should still use normal stroke with validation)
        float strokeWidth2 = cornerRadius * 0.82f; // 82% - just below 85%
        Debug.Log($"Test 2: Normal stroke (edge) - Corner radius {cornerRadius}, stroke {strokeWidth2:F1} ({strokeWidth2 / cornerRadius * 100:F0}% of radius)");
        procImg.strokeWidth = strokeWidth2;

        yield return new WaitForSeconds(3f);

        // Test 3: Just above threshold (should use simplified inner)
        float strokeWidth3 = cornerRadius * 0.87f; // 87% - just above 85%
        Debug.Log($"Test 3: Simplified inner - Corner radius {cornerRadius}, stroke {strokeWidth3:F1} ({strokeWidth3 / cornerRadius * 100:F0}% of radius)");
        procImg.strokeWidth = strokeWidth3;

        yield return new WaitForSeconds(3f);

        // Test 4: Well above threshold (should definitely use simplified inner)
        float strokeWidth4 = cornerRadius * 1.2f; // 120% - well above 85%
        Debug.Log($"Test 4: Simplified inner (clear) - Corner radius {cornerRadius}, stroke {strokeWidth4:F1} ({strokeWidth4 / cornerRadius * 100:F0}% of radius)");
        procImg.strokeWidth = strokeWidth4;

        yield return new WaitForSeconds(3f);

        // Test 5: Very small corner radius (stress test for validation)
        Debug.Log("Test 5: Very small corner radius stress test");
        procImg.cornerRadius = 2f;
        procImg.strokeWidth = 3f; // 150% of corner radius

        yield return new WaitForSeconds(3f);

        // Restore original values
        Debug.Log("Restoring original settings");
        procImg.cornerRadius = originalCornerRadius;
        procImg.strokeWidth = originalStrokeWidth;
        procImg.strokeLocation = originalStrokeLocation;
    }

    [ContextMenu("Test Max Radius Edge Case")]
    public void TestMaxRadiusEdgeCase()
    {
        ProceduralImage procImg = GetComponent<ProceduralImage>();
        if (procImg == null)
        {
            Debug.LogError("No ProceduralImage component found on this GameObject!");
            return;
        }

        StartCoroutine(TestMaxRadiusSequence(procImg));
    }

    private System.Collections.IEnumerator TestMaxRadiusSequence(ProceduralImage procImg)
    {
        // Store original values
        RectTransform rt = procImg.GetComponent<RectTransform>();
        Vector2 originalSize = rt.sizeDelta;
        float originalCornerRadius = procImg.cornerRadius;
        float originalStrokeWidth = procImg.strokeWidth;
        StrokeLocation originalStrokeLocation = procImg.strokeLocation;

        procImg.fillColor = Color.blue;
        procImg.strokeColor = Color.red;
        procImg.strokeLocation = StrokeLocation.Inside;

        // Set up a test case with max corner radius
        Vector2 testSize = new Vector2(100f, 60f); // min dimension = 60
        float maxRadius = Mathf.Min(testSize.x, testSize.y) * 0.5f; // = 30

        rt.sizeDelta = testSize;
        procImg.cornerRadius = maxRadius; // Perfect rounded rectangle (pill shape)

        // Test 1: Small stroke with max radius (should use normal stroke)
        float smallStroke = 8f; // Much less than corner radius (30)
        Debug.Log($"Test 1: Max radius ({maxRadius}) with small stroke ({smallStroke}) - Should use NORMAL stroke");
        procImg.strokeWidth = smallStroke;

        yield return new WaitForSeconds(4f);

        // Test 2: Medium stroke with max radius (still should prefer normal stroke if inner area is valid)
        float mediumStroke = 15f; // 50% of corner radius
        Debug.Log($"Test 2: Max radius ({maxRadius}) with medium stroke ({mediumStroke}) - Should prefer NORMAL stroke");
        procImg.strokeWidth = mediumStroke;

        yield return new WaitForSeconds(4f);

        // Test 3: Large stroke with max radius (should use simplified when inner area becomes too small)
        float largeStroke = 25f; // 83% of corner radius, but inner area becomes very small
        Debug.Log($"Test 3: Max radius ({maxRadius}) with large stroke ({largeStroke}) - Should use SIMPLIFIED stroke");
        procImg.strokeWidth = largeStroke;

        yield return new WaitForSeconds(4f);

        // Test 4: Compare with non-max radius case
        procImg.cornerRadius = 15f; // Half of max radius
        procImg.strokeWidth = 15f; // Equal to corner radius
        Debug.Log($"Test 4: Non-max radius (15) with equal stroke (15) - Should use SIMPLIFIED stroke");

        yield return new WaitForSeconds(4f);

        // Test 5: Center stroke with max radius
        procImg.cornerRadius = maxRadius;
        procImg.strokeWidth = 20f; // Inner stroke width = 10, much less than corner radius
        procImg.strokeLocation = StrokeLocation.Center;
        Debug.Log($"Test 5: Max radius ({maxRadius}) with center stroke (20, inner=10) - Should use NORMAL stroke");

        yield return new WaitForSeconds(4f);

        // Restore original values
        Debug.Log("Restoring original settings");
        rt.sizeDelta = originalSize;
        procImg.cornerRadius = originalCornerRadius;
        procImg.strokeWidth = originalStrokeWidth;
        procImg.strokeLocation = originalStrokeLocation;
    }
}
