using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demonstration script showing how to use the ProceduralImage component
/// </summary>
public class ProceduralImageDemo : MonoBehaviour
{
    [Header("Demo Controls")]
    [SerializeField] private ProceduralImage proceduralImage;
    [SerializeField] private Slider cornerRadiusSlider;
    [SerializeField] private Slider strokeWidthSlider;
    [SerializeField] private Toggle strokeToggle;
    [SerializeField] private Dropdown strokeLocationDropdown;

    private void Start()
    {
        // Find ProceduralImage if not assigned
        if (proceduralImage == null)
            proceduralImage = GetComponent<ProceduralImage>();

        // Setup demo controls if they exist
        SetupControls();
    }

    private void SetupControls()
    {
        if (cornerRadiusSlider != null)
        {
            cornerRadiusSlider.minValue = 0f;
            cornerRadiusSlider.maxValue = 50f;
            cornerRadiusSlider.value = proceduralImage.cornerRadius;
            cornerRadiusSlider.onValueChanged.AddListener(OnCornerRadiusChanged);
        }

        if (strokeWidthSlider != null)
        {
            strokeWidthSlider.minValue = 0f;
            strokeWidthSlider.maxValue = 10f;
            strokeWidthSlider.value = proceduralImage.strokeWidth;
            strokeWidthSlider.onValueChanged.AddListener(OnStrokeWidthChanged);
        }

        if (strokeToggle != null)
        {
            strokeToggle.isOn = proceduralImage.strokeWidth > 0;
            strokeToggle.onValueChanged.AddListener(OnStrokeToggleChanged);
        }

        if (strokeLocationDropdown != null)
        {
            strokeLocationDropdown.value = (int)proceduralImage.strokeLocation;
            strokeLocationDropdown.onValueChanged.AddListener(OnStrokeLocationChanged);
        }
    }

    private void OnCornerRadiusChanged(float value)
    {
        if (proceduralImage != null)
            proceduralImage.cornerRadius = value;
    }

    private void OnStrokeWidthChanged(float value)
    {
        if (proceduralImage != null)
            proceduralImage.strokeWidth = value;
    }

    private void OnStrokeToggleChanged(bool isOn)
    {
        if (proceduralImage != null)
        {
            proceduralImage.strokeWidth = isOn ? 2f : 0f;
            if (strokeWidthSlider != null)
                strokeWidthSlider.value = proceduralImage.strokeWidth;
        }
    }

    private void OnStrokeLocationChanged(int value)
    {
        if (proceduralImage != null)
            proceduralImage.strokeLocation = (StrokeLocation)value;
    }

    // Example methods for runtime manipulation
    public void SetRandomColors()
    {
        if (proceduralImage != null)
        {
            proceduralImage.fillColor = new Color(Random.value, Random.value, Random.value, 1f);
            proceduralImage.strokeColor = new Color(Random.value, Random.value, Random.value, 1f);
            proceduralImage.tintColor = new Color(Random.value, Random.value, Random.value, 1f);
        }
    }

    public void AnimateCornerRadius()
    {
        if (proceduralImage != null)
        {
            StartCoroutine(AnimateCornerRadiusCoroutine());
        }
    }

    private System.Collections.IEnumerator AnimateCornerRadiusCoroutine()
    {
        float duration = 2f;
        float startRadius = 0f;
        float endRadius = 25f;
        float elapsed = 0f;

        while (elapsed < duration)
        {
            elapsed += Time.deltaTime;
            float t = elapsed / duration;
            float radius = Mathf.Lerp(startRadius, endRadius, Mathf.Sin(t * Mathf.PI));
            proceduralImage.cornerRadius = radius;
            
            if (cornerRadiusSlider != null)
                cornerRadiusSlider.value = radius;
                
            yield return null;
        }
    }
}
