# ProceduralImage Testing Instructions

## Quick Test Setup

### Method 1: Using the Test Script
1. Create an empty GameObject in your scene
2. Add the `ProceduralImageTest` component to it
3. The component will automatically create a test ProceduralImage when the scene starts
4. You can also right-click the component and select "Create Test Procedural Image" from the context menu

### Method 2: Manual Setup
1. Create a Canvas (GameObject > UI > Canvas) if you don't have one
2. Create an empty GameObject as a child of the Canvas
3. Add a RectTransform component (should be added automatically)
4. Add the ProceduralImage component
5. Set the RectTransform size (e.g., 200x100)
6. Configure the ProceduralImage properties in the inspector

## Testing the Properties

### Shape Properties
- **Corner Radius**: Try values from 0 to 50
  - 0 = Perfect rectangle
  - Higher values = More rounded corners
  - The system automatically clamps to prevent invalid shapes

### Fill Properties
- **Fill Color**: Change the base color of the shape
- **Fill Texture**: Drag a texture from your project (optional)
- **Tint Color**: Multiplies with the fill texture color

### Stroke Properties
- **Stroke Location**: 
  - Center: Stroke centered on the shape boundary
  - Inside: Stroke drawn inside the shape
  - Outside: Stroke drawn outside the shape
- **Stroke Width**: Try values from 0 to 10
- **Stroke Color**: Color of the stroke outline

### Image Properties
- **Image**: Separate texture overlaid on the shape
- **Image Color**: Tint color for the image texture

## Expected Behavior

### What Should Work
✅ Properties should persist when changed in the inspector
✅ The shape should be visible immediately after adding the component
✅ Corner radius should automatically clamp to valid values
✅ Stroke should appear above the fill
✅ Component should work with Canvas masking
✅ Component should work with layout groups

### What to Test
1. **Property Persistence**: Change values in inspector, they should stay changed
2. **Visual Updates**: Changes should be reflected immediately in the Scene view
3. **Runtime Changes**: Properties can be modified via script during play mode
4. **Masking**: Add a Mask component to a parent object, it should clip the ProceduralImage
5. **Layout**: Add to a layout group, it should respond to layout changes

## Troubleshooting

### If Nothing Appears
- Check that the GameObject has a RectTransform with non-zero size
- Verify the Fill Color alpha is > 0
- Make sure the Canvas is set up correctly
- Check that the ProceduralImage is a child of the Canvas

### If Properties Don't Persist
- Make sure you're using the custom inspector (should show organized sections)
- Try clicking away from the inspector and back to refresh
- Check the Console for any error messages

### If Performance Issues
- Reduce corner radius for complex shapes
- Avoid very high corner radius values on small shapes

## Test Scenarios

### Basic Functionality
1. Create a simple rounded rectangle with corner radius 15
2. Add a stroke with width 3
3. Change stroke location between Inside/Center/Outside
4. Verify visual differences

### Edge Case Testing (Important!)
1. **Small Corner + Large Stroke**: Set corner radius to 5, stroke width to 20
   - Should not create overlapping/invalid geometry
   - Should show proper stroke without hiding fill
2. **Zero Corner + Large Stroke**: Set corner radius to 0, stroke width to 15
   - Should create clean rectangular stroke
3. **Large Corner + Large Stroke**: Set corner radius to 30, stroke width to 25
   - Should handle properly without geometry issues
4. **Very Small Shape**: Resize to 50x30, set corner radius 10, stroke width 8
   - Should handle gracefully without crashes
5. **Extreme Case**: Size 80x120, corner radius 0.5, stroke width 15
   - Fill should remain visible (not completely covered by stroke)
   - Stroke should not occupy entire rectangle
   - Use "Test Extreme Case" context menu for automated testing
6. **Three Algorithm Cases**: Test the different mesh generation algorithms
   - Algorithm 1 (Normal): Inner stroke width < corner radius
   - Algorithm 2 (Simplified): Inner stroke width >= corner radius
   - Algorithm 3 (Fill): Inner stroke width >= half minimum dimension
   - Use "Test Three Algorithm Cases" context menu for automated testing

### Advanced Features
1. Add a fill texture and adjust tint color
2. Add a separate image texture
3. Test with different Canvas render modes
4. Test with UI masking components

### Runtime Testing
1. Use the ProceduralImageDemo component for interactive controls
2. Test property changes via script during play mode
3. Verify performance with multiple ProceduralImages
4. Use "Test Edge Cases" context menu option for automated edge case testing

## Stroke Generation Algorithms

The component uses three different mesh generation algorithms based on the relationship between stroke width and corner radius:

### Inner Stroke Width Calculation
- **Outside Stroke**: Inner stroke width = 0
- **Inside Stroke**: Inner stroke width = stroke width
- **Center Stroke**: Inner stroke width = stroke width ÷ 2

### Algorithm Selection
1. **Normal Stroke** (Inner stroke width < corner radius)
   - Both outer and inner shapes have rounded corners
   - Standard stroke generation with proper corner arcs

2. **Simplified Inner** (Inner stroke width ≥ corner radius)
   - Outer shape has rounded corners, inner shape is simple rectangle (4 vertices)
   - Uses triangle fans from inner vertices to outer edge segments
   - Prevents corner overlap issues

3. **Fill Shape** (Inner stroke width ≥ half minimum dimension)
   - No inner cutout, stroke becomes a solid fill
   - Only outer boundary is generated with proper corner radius
   - Most efficient for very thick strokes

## Performance Notes
- Corner radius affects vertex count (higher radius = more vertices)
- Simple rectangles (radius = 0) are most efficient
- Algorithm 3 (Fill Shape) is most efficient for thick strokes
- Algorithm 2 (Simplified Inner) balances quality and performance
