using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

[AddComponentMenu("UI/Procedural Image")]
public class ProceduralImage : MaskableGraphic
{
    [Header("Shape")]
    [SerializeField] private float _cornerRadius = 0.0f;

    [Header("Fill")]
    [SerializeField] private Color _fillColor = Color.white;
    [SerializeField] private Texture2D _fillTexture = null;
    [SerializeField] private Color _tintColor = Color.white;

    [Header("Stroke")]
    [SerializeField] private StrokeLocation _strokeLocation = StrokeLocation.Center;
    [SerializeField] private float _strokeWidth = 0.0f;
    [SerializeField] private Color _strokeColor = Color.black;

    [Header("Image")]
    [SerializeField] private Texture2D _image = null;
    [SerializeField] private Color _imageColor = Color.white;

    // Properties with validation
    public float cornerRadius
    {
        get => _cornerRadius;
        set
        {
            float maxRadius = Mathf.Min(rectTransform.rect.width, rectTransform.rect.height) * 0.5f;
            _cornerRadius = Mathf.Clamp(value, 0f, maxRadius);
            SetVerticesDirty();
        }
    }

    public Color fillColor
    {
        get => _fillColor;
        set
        {
            _fillColor = value;
            SetVerticesDirty();
        }
    }

    public Texture2D fillTexture
    {
        get => _fillTexture;
        set
        {
            _fillTexture = value;
            SetMaterialDirty();
        }
    }

    public Color tintColor
    {
        get => _tintColor;
        set
        {
            _tintColor = value;
            SetVerticesDirty();
        }
    }

    public StrokeLocation strokeLocation
    {
        get => _strokeLocation;
        set
        {
            _strokeLocation = value;
            SetVerticesDirty();
        }
    }

    public float strokeWidth
    {
        get => _strokeWidth;
        set
        {
            _strokeWidth = Mathf.Max(0f, value);
            SetVerticesDirty();
        }
    }

    public Color strokeColor
    {
        get => _strokeColor;
        set
        {
            _strokeColor = value;
            SetVerticesDirty();
        }
    }

    public Texture2D image
    {
        get => _image;
        set
        {
            _image = value;
            SetMaterialDirty();
        }
    }

    public Color imageColor
    {
        get => _imageColor;
        set
        {
            _imageColor = value;
            SetVerticesDirty();
        }
    }

    public override Texture mainTexture
    {
        get
        {
            if (_fillTexture != null)
                return _fillTexture;
            if (_image != null)
                return _image;
            return s_WhiteTexture;
        }
    }

    protected override void OnValidate()
    {
        base.OnValidate();

        // Clamp corner radius to valid range
        float maxRadius = Mathf.Min(rectTransform.rect.width, rectTransform.rect.height) * 0.5f;
        _cornerRadius = Mathf.Clamp(_cornerRadius, 0f, maxRadius);
        _strokeWidth = Mathf.Max(0f, _strokeWidth);
    }

    protected override void OnRectTransformDimensionsChange()
    {
        base.OnRectTransformDimensionsChange();

        // Re-clamp corner radius when dimensions change
        float maxRadius = Mathf.Min(rectTransform.rect.width, rectTransform.rect.height) * 0.5f;
        _cornerRadius = Mathf.Clamp(_cornerRadius, 0f, maxRadius);
    }

    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();

        Rect rect = GetPixelAdjustedRect();
        Vector4 v = new Vector4(rect.x, rect.y, rect.x + rect.width, rect.y + rect.height);

        float width = rect.width;
        float height = rect.height;

        // Ensure corner radius doesn't exceed half the minimum dimension
        float maxRadius = Mathf.Min(width, height) * 0.5f;
        float actualCornerRadius = Mathf.Min(_cornerRadius, maxRadius);

        // Generate fill mesh
        if (_fillColor.a > 0 || _fillTexture != null)
        {
            GenerateFillMesh(vh, v, actualCornerRadius, _fillColor * _tintColor);
        }

        // Generate stroke mesh
        if (_strokeWidth > 0 && _strokeColor.a > 0)
        {
            GenerateStrokeMesh(vh, v, actualCornerRadius, _strokeWidth, _strokeColor, _strokeLocation);
        }

        // Generate image mesh (if different from fill texture)
        if (_image != null && _image != _fillTexture && _imageColor.a > 0)
        {
            GenerateImageMesh(vh, v, actualCornerRadius, _imageColor);
        }
    }

    private void GenerateFillMesh(VertexHelper vh, Vector4 v, float cornerRadius, Color color)
    {
        if (cornerRadius <= 0)
        {
            // Simple rectangle
            AddQuad(vh, new Vector2(v.x, v.y), new Vector2(v.z, v.w), color, Vector2.zero, Vector2.one);
        }
        else
        {
            // Rounded rectangle
            GenerateRoundedRectangle(vh, v, cornerRadius, color, Vector2.zero, Vector2.one);
        }
    }

    private void GenerateStrokeMesh(VertexHelper vh, Vector4 v, float cornerRadius, float strokeWidth, Color strokeColor, StrokeLocation location)
    {
        Vector4 outerRect = v;
        Vector4 innerRect = v;

        switch (location)
        {
            case StrokeLocation.Inside:
                innerRect = new Vector4(v.x + strokeWidth, v.y + strokeWidth, v.z - strokeWidth, v.w - strokeWidth);
                break;
            case StrokeLocation.Outside:
                outerRect = new Vector4(v.x - strokeWidth, v.y - strokeWidth, v.z + strokeWidth, v.w + strokeWidth);
                break;
            case StrokeLocation.Center:
                float halfStroke = strokeWidth * 0.5f;
                outerRect = new Vector4(v.x - halfStroke, v.y - halfStroke, v.z + halfStroke, v.w + halfStroke);
                innerRect = new Vector4(v.x + halfStroke, v.y + halfStroke, v.z - halfStroke, v.w - halfStroke);
                break;
        }

        // Adjust corner radius for stroke
        float outerCornerRadius = cornerRadius + (location == StrokeLocation.Outside ? strokeWidth : (location == StrokeLocation.Center ? strokeWidth * 0.5f : 0));
        float innerCornerRadius = Mathf.Max(0, cornerRadius - (location == StrokeLocation.Inside ? strokeWidth : (location == StrokeLocation.Center ? strokeWidth * 0.5f : 0)));

        if (outerCornerRadius <= 0 && innerCornerRadius <= 0)
        {
            // Simple rectangle stroke
            GenerateRectangleStroke(vh, outerRect, innerRect, strokeColor);
        }
        else
        {
            // Rounded rectangle stroke
            GenerateRoundedRectangleStroke(vh, outerRect, innerRect, outerCornerRadius, innerCornerRadius, strokeColor);
        }
    }

    private void GenerateImageMesh(VertexHelper vh, Vector4 v, float cornerRadius, Color imageColor)
    {
        // Image uses the same shape as fill but with different UV mapping
        if (cornerRadius <= 0)
        {
            AddQuad(vh, new Vector2(v.x, v.y), new Vector2(v.z, v.w), imageColor, Vector2.zero, Vector2.one);
        }
        else
        {
            GenerateRoundedRectangle(vh, v, cornerRadius, imageColor, Vector2.zero, Vector2.one);
        }
    }

    private void AddQuad(VertexHelper vh, Vector2 posMin, Vector2 posMax, Color color, Vector2 uvMin, Vector2 uvMax)
    {
        int startIndex = vh.currentVertCount;

        vh.AddVert(new Vector3(posMin.x, posMin.y, 0), color, new Vector2(uvMin.x, uvMin.y));
        vh.AddVert(new Vector3(posMin.x, posMax.y, 0), color, new Vector2(uvMin.x, uvMax.y));
        vh.AddVert(new Vector3(posMax.x, posMax.y, 0), color, new Vector2(uvMax.x, uvMax.y));
        vh.AddVert(new Vector3(posMax.x, posMin.y, 0), color, new Vector2(uvMax.x, uvMin.y));

        vh.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vh.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
    }

    private void GenerateRoundedRectangle(VertexHelper vh, Vector4 v, float cornerRadius, Color color, Vector2 uvMin, Vector2 uvMax)
    {
        int segments = Mathf.Max(4, Mathf.RoundToInt(cornerRadius * 0.5f));

        List<Vector2> vertices = new List<Vector2>();
        List<Vector2> uvs = new List<Vector2>();

        float width = v.z - v.x;
        float height = v.w - v.y;

        // Center point
        Vector2 center = new Vector2((v.x + v.z) * 0.5f, (v.y + v.w) * 0.5f);
        vertices.Add(center);
        uvs.Add((uvMin + uvMax) * 0.5f);

        // Generate corner vertices
        Vector2[] corners = {
            new Vector2(v.x + cornerRadius, v.y + cornerRadius), // Bottom-left
            new Vector2(v.z - cornerRadius, v.y + cornerRadius), // Bottom-right
            new Vector2(v.z - cornerRadius, v.w - cornerRadius), // Top-right
            new Vector2(v.x + cornerRadius, v.w - cornerRadius)  // Top-left
        };

        // Add corner arc vertices
        for (int corner = 0; corner < 4; corner++)
        {
            float startAngle = corner * 90f * Mathf.Deg2Rad;
            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + (i * 90f * Mathf.Deg2Rad / segments);
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * cornerRadius;
                Vector2 pos = corners[corner] + offset;

                vertices.Add(pos);

                // Calculate UV
                Vector2 uv = new Vector2(
                    Mathf.Lerp(uvMin.x, uvMax.x, (pos.x - v.x) / width),
                    Mathf.Lerp(uvMin.y, uvMax.y, (pos.y - v.y) / height)
                );
                uvs.Add(uv);
            }
        }

        // Add vertices to mesh
        int startIndex = vh.currentVertCount;
        for (int i = 0; i < vertices.Count; i++)
        {
            vh.AddVert(new Vector3(vertices[i].x, vertices[i].y, 0), color, uvs[i]);
        }

        // Add triangles
        int verticesPerCorner = segments + 1;
        for (int corner = 0; corner < 4; corner++)
        {
            int cornerStartIndex = 1 + corner * verticesPerCorner;
            for (int i = 0; i < segments; i++)
            {
                int current = startIndex + cornerStartIndex + i;
                int next = startIndex + cornerStartIndex + i + 1;
                vh.AddTriangle(startIndex, current, next);
            }
        }
    }

    private void GenerateRectangleStroke(VertexHelper vh, Vector4 outer, Vector4 inner, Color strokeColor)
    {
        // Top
        AddQuad(vh, new Vector2(outer.x, inner.w), new Vector2(outer.z, outer.w), strokeColor, Vector2.zero, Vector2.one);
        // Bottom
        AddQuad(vh, new Vector2(outer.x, outer.y), new Vector2(outer.z, inner.y), strokeColor, Vector2.zero, Vector2.one);
        // Left
        AddQuad(vh, new Vector2(outer.x, inner.y), new Vector2(inner.x, inner.w), strokeColor, Vector2.zero, Vector2.one);
        // Right
        AddQuad(vh, new Vector2(inner.z, inner.y), new Vector2(outer.z, inner.w), strokeColor, Vector2.zero, Vector2.one);
    }

    private void GenerateRoundedRectangleStroke(VertexHelper vh, Vector4 outer, Vector4 inner, float outerRadius, float innerRadius, Color strokeColor)
    {
        // For simplicity, generate as difference between two rounded rectangles
        // This is a complex operation that would require more sophisticated mesh generation
        // For now, fall back to simple rectangle stroke
        GenerateRectangleStroke(vh, outer, inner, strokeColor);
    }
}

public enum StrokeLocation
{
    Center,
    Inside,
    Outside
}