using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

[AddComponentMenu("UI/Procedural Image")]
public class ProceduralImage : MaskableGraphic
{
    [Header("Shape")]
    [SerializeField] private float _cornerRadius = 0.0f;

    [Header("Fill")]
    [SerializeField] private Color _fillColor = Color.white;
    [SerializeField] private Texture2D _fillTexture = null;
    [SerializeField] private Color _tintColor = Color.white;

    [Header("Stroke")]
    [SerializeField] private StrokeLocation _strokeLocation = StrokeLocation.Center;
    [SerializeField] private float _strokeWidth = 0.0f;
    [SerializeField] private Color _strokeColor = Color.black;

    [Header("Image")]
    [SerializeField] private Texture2D _image = null;
    [SerializeField] private Color _imageColor = Color.white;

    // Properties with validation
    public float cornerRadius
    {
        get => _cornerRadius;
        set
        {
            if (rectTransform != null)
            {
                float maxRadius = Mathf.Min(rectTransform.rect.width, rectTransform.rect.height) * 0.5f;
                _cornerRadius = Mathf.Clamp(value, 0f, maxRadius);
            }
            else
            {
                _cornerRadius = Mathf.Max(0f, value);
            }
            SetVerticesDirty();
        }
    }

    public Color fillColor
    {
        get => _fillColor;
        set
        {
            _fillColor = value;
            SetVerticesDirty();
        }
    }

    public Texture2D fillTexture
    {
        get => _fillTexture;
        set
        {
            _fillTexture = value;
            SetMaterialDirty();
        }
    }

    public Color tintColor
    {
        get => _tintColor;
        set
        {
            _tintColor = value;
            SetVerticesDirty();
        }
    }

    public StrokeLocation strokeLocation
    {
        get => _strokeLocation;
        set
        {
            _strokeLocation = value;
            SetVerticesDirty();
        }
    }

    public float strokeWidth
    {
        get => _strokeWidth;
        set
        {
            _strokeWidth = Mathf.Max(0f, value);
            SetVerticesDirty();
        }
    }

    public Color strokeColor
    {
        get => _strokeColor;
        set
        {
            _strokeColor = value;
            SetVerticesDirty();
        }
    }

    public Texture2D image
    {
        get => _image;
        set
        {
            _image = value;
            SetMaterialDirty();
        }
    }

    public Color imageColor
    {
        get => _imageColor;
        set
        {
            _imageColor = value;
            SetVerticesDirty();
        }
    }

    public override Texture mainTexture
    {
        get
        {
            if (_fillTexture != null)
                return _fillTexture;
            if (_image != null)
                return _image;
            return s_WhiteTexture;
        }
    }

    protected override void Awake()
    {
        base.Awake();

        // Ensure we have a default material
        if (material == null)
            material = defaultMaterial;
    }

    protected override void Start()
    {
        base.Start();

        // Force initial mesh generation
        SetVerticesDirty();
    }

    protected override void Reset()
    {
        base.Reset();

        // Set default values when component is first added
        _cornerRadius = 0f;
        _fillColor = Color.white;
        _fillTexture = null;
        _tintColor = Color.white;
        _strokeLocation = StrokeLocation.Center;
        _strokeWidth = 0f;
        _strokeColor = Color.black;
        _image = null;
        _imageColor = Color.white;

        // Ensure we have proper material
        if (material == null)
            material = defaultMaterial;
    }

    protected override void OnValidate()
    {
        base.OnValidate();

        // Clamp corner radius to valid range
        if (rectTransform != null)
        {
            float maxRadius = Mathf.Min(rectTransform.rect.width, rectTransform.rect.height) * 0.5f;
            _cornerRadius = Mathf.Clamp(_cornerRadius, 0f, maxRadius);
        }
        _strokeWidth = Mathf.Max(0f, _strokeWidth);

        // Force mesh update
        SetVerticesDirty();
    }

    protected override void OnRectTransformDimensionsChange()
    {
        base.OnRectTransformDimensionsChange();

        // Re-clamp corner radius when dimensions change
        if (rectTransform != null)
        {
            float maxRadius = Mathf.Min(rectTransform.rect.width, rectTransform.rect.height) * 0.5f;
            _cornerRadius = Mathf.Clamp(_cornerRadius, 0f, maxRadius);
        }
    }

    protected override void OnPopulateMesh(VertexHelper vh)
    {
        vh.Clear();

        Rect rect = GetPixelAdjustedRect();
        Vector4 v = new Vector4(rect.x, rect.y, rect.x + rect.width, rect.y + rect.height);

        float width = rect.width;
        float height = rect.height;

        // Ensure corner radius doesn't exceed half the minimum dimension
        float maxRadius = Mathf.Min(width, height) * 0.5f;
        float actualCornerRadius = Mathf.Min(_cornerRadius, maxRadius);

        // Check if stroke completely fills the shape
        bool strokeFillsShape = false;
        if (_strokeWidth > 0)
        {
            float innerWidth = width - (_strokeLocation == StrokeLocation.Inside ? _strokeWidth * 2 : (_strokeLocation == StrokeLocation.Center ? _strokeWidth : 0));
            float innerHeight = height - (_strokeLocation == StrokeLocation.Inside ? _strokeWidth * 2 : (_strokeLocation == StrokeLocation.Center ? _strokeWidth : 0));
            strokeFillsShape = (innerWidth <= 0 || innerHeight <= 0);
        }

        // Generate fill mesh only if stroke doesn't completely fill the shape
        if (!strokeFillsShape)
        {
            Color finalFillColor = _fillColor * _tintColor;
            if (finalFillColor.a <= 0 && _fillTexture == null)
            {
                // If no visible fill, use a very transparent white to ensure something renders
                finalFillColor = new Color(1f, 1f, 1f, 0.01f);
            }
            GenerateFillMesh(vh, v, actualCornerRadius, finalFillColor);
        }

        // Generate stroke mesh
        if (_strokeWidth > 0 && _strokeColor.a > 0)
        {
            GenerateStrokeMesh(vh, v, actualCornerRadius, _strokeWidth, _strokeColor, _strokeLocation);
        }

        // Generate image mesh (if different from fill texture and stroke doesn't fill shape)
        if (_image != null && _image != _fillTexture && _imageColor.a > 0 && !strokeFillsShape)
        {
            GenerateImageMesh(vh, v, actualCornerRadius, _imageColor);
        }
    }

    private void GenerateFillMesh(VertexHelper vh, Vector4 v, float cornerRadius, Color color)
    {
        if (cornerRadius <= 0)
        {
            // Simple rectangle
            AddQuad(vh, new Vector2(v.x, v.y), new Vector2(v.z, v.w), color, Vector2.zero, Vector2.one);
        }
        else
        {
            // Rounded rectangle
            GenerateRoundedRectangle(vh, v, cornerRadius, color, Vector2.zero, Vector2.one);
        }
    }

    private void GenerateStrokeMesh(VertexHelper vh, Vector4 v, float cornerRadius, float strokeWidth, Color strokeColor, StrokeLocation location)
    {
        // 0. When stroke width is 0 => generate no mesh
        if (strokeWidth <= 0) return;

        // Calculate inner and outer stroke widths
        float innerStrokeWidth = GetInnerStrokeWidth(strokeWidth, location);
        float outerStrokeWidth = GetOuterStrokeWidth(strokeWidth, location);

        // Calculate shape dimensions
        float shapeWidth = v.z - v.x;
        float shapeHeight = v.w - v.y;
        float minShapeDimension = Mathf.Min(shapeWidth, shapeHeight);

        // Calculate outer and inner rectangles
        Vector4 outerRect = new Vector4(
            v.x - outerStrokeWidth, v.y - outerStrokeWidth,
            v.z + outerStrokeWidth, v.w + outerStrokeWidth);
        Vector4 innerRect = new Vector4(
            v.x + innerStrokeWidth, v.y + innerStrokeWidth,
            v.z - innerStrokeWidth, v.w - innerStrokeWidth);

        // Calculate corner radii
        float outerCornerRadius = cornerRadius + outerStrokeWidth;
        float innerCornerRadius = cornerRadius - innerStrokeWidth;

        // 3. When inner stroke width >= half of minimum dimension => no interior, generate as fill
        if (innerStrokeWidth >= minShapeDimension * 0.5f)
        {
            GenerateStrokeAsFillShape(vh, outerRect, outerCornerRadius, strokeColor);
        }
        // 2. When inner stroke width >= corner radius => use simplified inner shape
        else if (innerStrokeWidth >= cornerRadius)
        {
            GenerateStrokeWithSimplifiedInner(vh, outerRect, innerRect, outerCornerRadius, strokeColor);
        }
        // 1. When inner stroke width < corner radius => use normal stroke generation
        else
        {
            GenerateNormalStroke(vh, outerRect, innerRect, outerCornerRadius, innerCornerRadius, strokeColor);
        }
    }

    private float GetInnerStrokeWidth(float strokeWidth, StrokeLocation location)
    {
        switch (location)
        {
            case StrokeLocation.Outside: return 0f;
            case StrokeLocation.Inside: return strokeWidth;
            case StrokeLocation.Center: return strokeWidth * 0.5f;
            default: return 0f;
        }
    }

    private float GetOuterStrokeWidth(float strokeWidth, StrokeLocation location)
    {
        switch (location)
        {
            case StrokeLocation.Outside: return strokeWidth;
            case StrokeLocation.Inside: return 0f;
            case StrokeLocation.Center: return strokeWidth * 0.5f;
            default: return 0f;
        }
    }

    // Case 3: Generate stroke as fill shape (no interior)
    private void GenerateStrokeAsFillShape(VertexHelper vh, Vector4 outerRect, float outerCornerRadius, Color strokeColor)
    {
        if (outerCornerRadius <= 0)
        {
            AddQuad(vh, new Vector2(outerRect.x, outerRect.y), new Vector2(outerRect.z, outerRect.w), strokeColor, Vector2.zero, Vector2.one);
        }
        else
        {
            GenerateRoundedRectangle(vh, outerRect, outerCornerRadius, strokeColor, Vector2.zero, Vector2.one);
        }
    }

    // Case 2: Generate stroke with simplified inner shape (4 vertices)
    private void GenerateStrokeWithSimplifiedInner(VertexHelper vh, Vector4 outerRect, Vector4 innerRect, float outerCornerRadius, Color strokeColor)
    {
        int segments = Mathf.Max(4, Mathf.RoundToInt(outerCornerRadius * 0.5f));

        // Generate outer corner vertices for each corner
        List<List<Vector2>> cornerVertices = new List<List<Vector2>>();

        // Corner centers for outer shape
        Vector2[] outerCornerCenters = {
            new Vector2(outerRect.x + outerCornerRadius, outerRect.y + outerCornerRadius), // Bottom-left
            new Vector2(outerRect.z - outerCornerRadius, outerRect.y + outerCornerRadius), // Bottom-right
            new Vector2(outerRect.z - outerCornerRadius, outerRect.w - outerCornerRadius), // Top-right
            new Vector2(outerRect.x + outerCornerRadius, outerRect.w - outerCornerRadius)  // Top-left
        };

        // Starting angles for each corner
        float[] startAngles = {
            Mathf.PI,        // Bottom-left: start from left (180°)
            3f * Mathf.PI / 2f, // Bottom-right: start from bottom (270°)
            0f,              // Top-right: start from right (0°)
            Mathf.PI / 2f    // Top-left: start from top (90°)
        };

        // Generate vertices for each corner arc
        for (int corner = 0; corner < 4; corner++)
        {
            List<Vector2> cornerArc = new List<Vector2>();
            float startAngle = startAngles[corner];

            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + (i * Mathf.PI * 0.5f / segments);
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * outerCornerRadius;
                cornerArc.Add(outerCornerCenters[corner] + offset);
            }
            cornerVertices.Add(cornerArc);
        }

        // Inner vertices (4 corners only)
        Vector2[] innerVertices = {
            new Vector2(innerRect.x, innerRect.y), // Bottom-left
            new Vector2(innerRect.z, innerRect.y), // Bottom-right
            new Vector2(innerRect.z, innerRect.w), // Top-right
            new Vector2(innerRect.x, innerRect.w)  // Top-left
        };

        // Generate stroke as 4 quarter-circles + 4 connecting rectangles
        GenerateQuarterCircleStroke(vh, cornerVertices, innerVertices, strokeColor);
    }

    // Case 1: Generate normal stroke (both inner and outer have rounded corners)
    private void GenerateNormalStroke(VertexHelper vh, Vector4 outerRect, Vector4 innerRect, float outerCornerRadius, float innerCornerRadius, Color strokeColor)
    {
        // Clamp radii to valid ranges
        float outerWidth = outerRect.z - outerRect.x;
        float outerHeight = outerRect.w - outerRect.y;
        float innerWidth = innerRect.z - innerRect.x;
        float innerHeight = innerRect.w - innerRect.y;

        outerCornerRadius = Mathf.Clamp(outerCornerRadius, 0f, Mathf.Min(outerWidth, outerHeight) * 0.5f);
        innerCornerRadius = Mathf.Clamp(innerCornerRadius, 0f, Mathf.Min(innerWidth, innerHeight) * 0.5f);

        if (outerCornerRadius <= 0 && innerCornerRadius <= 0)
        {
            // Simple rectangle stroke
            GenerateRectangleStroke(vh, outerRect, innerRect, strokeColor);
        }
        else
        {
            // Rounded rectangle stroke
            GenerateRoundedRectangleStroke(vh, outerRect, innerRect, outerCornerRadius, innerCornerRadius, strokeColor);
        }
    }

    private void GenerateQuarterCircleStroke(VertexHelper vh, List<List<Vector2>> cornerVertices, Vector2[] innerVertices, Color strokeColor)
    {
        // Generate 4 quarter-circle strokes (one for each corner)
        for (int corner = 0; corner < 4; corner++)
        {
            GenerateCornerStroke(vh, cornerVertices[corner], innerVertices[corner], strokeColor);
        }

        // Generate 4 connecting rectangles between corners
        for (int side = 0; side < 4; side++)
        {
            int nextSide = (side + 1) % 4;
            GenerateSideStroke(vh, cornerVertices[side], cornerVertices[nextSide], innerVertices[side], innerVertices[nextSide], strokeColor);
        }
    }

    private void GenerateCornerStroke(VertexHelper vh, List<Vector2> outerCornerVertices, Vector2 innerCornerVertex, Color strokeColor)
    {
        int startIndex = vh.currentVertCount;

        // Add inner corner vertex
        vh.AddVert(new Vector3(innerCornerVertex.x, innerCornerVertex.y, 0), strokeColor, Vector2.zero);

        // Add all outer corner vertices
        foreach (var vertex in outerCornerVertices)
        {
            vh.AddVert(new Vector3(vertex.x, vertex.y, 0), strokeColor, Vector2.zero);
        }

        // Create triangle fan from inner vertex to outer arc
        for (int i = 0; i < outerCornerVertices.Count - 1; i++)
        {
            vh.AddTriangle(startIndex, startIndex + 1 + i, startIndex + 1 + i + 1);
        }
    }

    private void GenerateSideStroke(VertexHelper vh, List<Vector2> corner1Vertices, List<Vector2> corner2Vertices, Vector2 inner1, Vector2 inner2, Color strokeColor)
    {
        // Get the connecting vertices between corners
        Vector2 outer1 = corner1Vertices[corner1Vertices.Count - 1]; // Last vertex of first corner
        Vector2 outer2 = corner2Vertices[0]; // First vertex of second corner

        // Create rectangle between the two corners
        int startIndex = vh.currentVertCount;

        vh.AddVert(new Vector3(inner1.x, inner1.y, 0), strokeColor, Vector2.zero);     // 0
        vh.AddVert(new Vector3(inner2.x, inner2.y, 0), strokeColor, Vector2.zero);     // 1
        vh.AddVert(new Vector3(outer2.x, outer2.y, 0), strokeColor, Vector2.zero);     // 2
        vh.AddVert(new Vector3(outer1.x, outer1.y, 0), strokeColor, Vector2.zero);     // 3

        // Create two triangles for the rectangle
        vh.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vh.AddTriangle(startIndex, startIndex + 2, startIndex + 3);
    }

    private void GenerateTriangleFansStroke(VertexHelper vh, List<Vector2> outerVertices, List<Vector2> innerVertices, Color strokeColor)
    {
        int startIndex = vh.currentVertCount;

        // Add all vertices
        foreach (var vertex in outerVertices)
        {
            vh.AddVert(new Vector3(vertex.x, vertex.y, 0), strokeColor, Vector2.zero);
        }
        foreach (var vertex in innerVertices)
        {
            vh.AddVert(new Vector3(vertex.x, vertex.y, 0), strokeColor, Vector2.zero);
        }

        int outerCount = outerVertices.Count;
        int innerCount = innerVertices.Count;

        // Create triangle fans from each inner vertex to corresponding outer segments
        int outerVerticesPerInner = outerCount / innerCount;

        for (int i = 0; i < innerCount; i++)
        {
            int innerIndex = startIndex + outerCount + i;
            int nextInnerIndex = startIndex + outerCount + ((i + 1) % innerCount);

            // Calculate outer vertex range for this inner vertex
            int outerStart = i * outerVerticesPerInner;
            int outerEnd = ((i + 1) * outerVerticesPerInner) % outerCount;

            // Create triangles from inner vertex to outer edge
            for (int j = outerStart; j != outerEnd; j = (j + 1) % outerCount)
            {
                int outerCurrent = startIndex + j;
                int outerNext = startIndex + ((j + 1) % outerCount);

                vh.AddTriangle(innerIndex, outerCurrent, outerNext);
            }

            // Connect to next inner vertex
            if (outerEnd != outerStart)
            {
                vh.AddTriangle(innerIndex, startIndex + outerEnd, nextInnerIndex);
            }
        }
    }

    private void GenerateImageMesh(VertexHelper vh, Vector4 v, float cornerRadius, Color imageColor)
    {
        // Image uses the same shape as fill but with different UV mapping
        if (cornerRadius <= 0)
        {
            AddQuad(vh, new Vector2(v.x, v.y), new Vector2(v.z, v.w), imageColor, Vector2.zero, Vector2.one);
        }
        else
        {
            GenerateRoundedRectangle(vh, v, cornerRadius, imageColor, Vector2.zero, Vector2.one);
        }
    }

    private void AddQuad(VertexHelper vh, Vector2 posMin, Vector2 posMax, Color color, Vector2 uvMin, Vector2 uvMax)
    {
        int startIndex = vh.currentVertCount;

        vh.AddVert(new Vector3(posMin.x, posMin.y, 0), color, new Vector2(uvMin.x, uvMin.y));
        vh.AddVert(new Vector3(posMin.x, posMax.y, 0), color, new Vector2(uvMin.x, uvMax.y));
        vh.AddVert(new Vector3(posMax.x, posMax.y, 0), color, new Vector2(uvMax.x, uvMax.y));
        vh.AddVert(new Vector3(posMax.x, posMin.y, 0), color, new Vector2(uvMax.x, uvMin.y));

        vh.AddTriangle(startIndex, startIndex + 1, startIndex + 2);
        vh.AddTriangle(startIndex + 2, startIndex + 3, startIndex);
    }

    private void GenerateRoundedRectangle(VertexHelper vh, Vector4 v, float cornerRadius, Color color, Vector2 uvMin, Vector2 uvMax)
    {
        int segments = Mathf.Max(4, Mathf.RoundToInt(cornerRadius * 0.5f));

        List<Vector2> vertices = new List<Vector2>();
        List<Vector2> uvs = new List<Vector2>();

        float width = v.z - v.x;
        float height = v.w - v.y;

        // Center point
        Vector2 center = new Vector2((v.x + v.z) * 0.5f, (v.y + v.w) * 0.5f);
        vertices.Add(center);
        uvs.Add((uvMin + uvMax) * 0.5f);

        // Generate corner vertices - fix the corner positioning and angles
        Vector2[] cornerCenters = {
            new Vector2(v.x + cornerRadius, v.y + cornerRadius), // Bottom-left
            new Vector2(v.z - cornerRadius, v.y + cornerRadius), // Bottom-right
            new Vector2(v.z - cornerRadius, v.w - cornerRadius), // Top-right
            new Vector2(v.x + cornerRadius, v.w - cornerRadius)  // Top-left
        };

        // Correct starting angles for each corner (in radians)
        float[] startAngles = {
            Mathf.PI,        // Bottom-left: start from left (180°)
            3f * Mathf.PI / 2f, // Bottom-right: start from bottom (270°)
            0f,              // Top-right: start from right (0°)
            Mathf.PI / 2f    // Top-left: start from top (90°)
        };

        // Add corner arc vertices
        for (int corner = 0; corner < 4; corner++)
        {
            float startAngle = startAngles[corner];
            for (int i = 0; i <= segments; i++)
            {
                float angle = startAngle + (i * Mathf.PI * 0.5f / segments);
                Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * cornerRadius;
                Vector2 pos = cornerCenters[corner] + offset;

                vertices.Add(pos);

                // Calculate UV
                Vector2 uv = new Vector2(
                    Mathf.Lerp(uvMin.x, uvMax.x, (pos.x - v.x) / width),
                    Mathf.Lerp(uvMin.y, uvMax.y, (pos.y - v.y) / height)
                );
                uvs.Add(uv);
            }
        }

        // Add vertices to mesh
        int startIndex = vh.currentVertCount;
        for (int i = 0; i < vertices.Count; i++)
        {
            vh.AddVert(new Vector3(vertices[i].x, vertices[i].y, 0), color, uvs[i]);
        }

        // Add triangles - create a fan from center to all perimeter vertices
        int totalPerimeterVertices = vertices.Count - 1; // Exclude center vertex
        for (int i = 1; i < totalPerimeterVertices; i++)
        {
            int current = startIndex + i;
            int next = startIndex + i + 1;
            vh.AddTriangle(startIndex, current, next);
        }

        // Close the shape by connecting last vertex back to first perimeter vertex
        vh.AddTriangle(startIndex, startIndex + totalPerimeterVertices, startIndex + 1);
    }

    private void GenerateRectangleStroke(VertexHelper vh, Vector4 outer, Vector4 inner, Color strokeColor)
    {
        // Top
        AddQuad(vh, new Vector2(outer.x, inner.w), new Vector2(outer.z, outer.w), strokeColor, Vector2.zero, Vector2.one);
        // Bottom
        AddQuad(vh, new Vector2(outer.x, outer.y), new Vector2(outer.z, inner.y), strokeColor, Vector2.zero, Vector2.one);
        // Left
        AddQuad(vh, new Vector2(outer.x, inner.y), new Vector2(inner.x, inner.w), strokeColor, Vector2.zero, Vector2.one);
        // Right
        AddQuad(vh, new Vector2(inner.z, inner.y), new Vector2(outer.z, inner.w), strokeColor, Vector2.zero, Vector2.one);
    }

    private void GenerateRoundedRectangleStroke(VertexHelper vh, Vector4 outer, Vector4 inner, float outerRadius, float innerRadius, Color strokeColor)
    {
        // Generate stroke as the difference between outer and inner rounded rectangles
        int segments = Mathf.Max(4, Mathf.RoundToInt(Mathf.Max(outerRadius, innerRadius) * 0.5f));

        List<Vector2> outerVertices = GenerateRoundedRectangleVertices(outer, outerRadius, segments);
        List<Vector2> innerVertices = GenerateRoundedRectangleVertices(inner, innerRadius, segments);



        int vertexCount = Mathf.Min(outerVertices.Count, innerVertices.Count);
        int startIndex = vh.currentVertCount;

        // Add all vertices
        for (int i = 0; i < vertexCount; i++)
        {
            vh.AddVert(new Vector3(outerVertices[i].x, outerVertices[i].y, 0), strokeColor, Vector2.zero);
            vh.AddVert(new Vector3(innerVertices[i].x, innerVertices[i].y, 0), strokeColor, Vector2.zero);
        }

        // Add triangles to form the stroke
        for (int i = 0; i < vertexCount; i++)
        {
            int next = (i + 1) % vertexCount;

            int outerCurrent = startIndex + i * 2;
            int innerCurrent = startIndex + i * 2 + 1;
            int outerNext = startIndex + next * 2;
            int innerNext = startIndex + next * 2 + 1;

            // Create quad between current and next vertex pairs
            vh.AddTriangle(outerCurrent, outerNext, innerCurrent);
            vh.AddTriangle(innerCurrent, outerNext, innerNext);
        }
    }


    private List<Vector2> GenerateRoundedRectangleVertices(Vector4 rect, float cornerRadius, int segments)
    {
        List<Vector2> vertices = new List<Vector2>();

        float width = rect.z - rect.x;
        float height = rect.w - rect.y;

        // Clamp corner radius to prevent invalid geometry
        float maxRadius = Mathf.Min(width, height) * 0.5f;
        cornerRadius = Mathf.Clamp(cornerRadius, 0f, maxRadius);

        if (cornerRadius <= 0 || width <= 0 || height <= 0)
        {
            // Simple rectangle vertices or degenerate case
            vertices.Add(new Vector2(rect.x, rect.y)); // Bottom-left
            vertices.Add(new Vector2(rect.z, rect.y)); // Bottom-right
            vertices.Add(new Vector2(rect.z, rect.w)); // Top-right
            vertices.Add(new Vector2(rect.x, rect.w)); // Top-left
            return vertices;
        }

        // Corner centers
        Vector2[] cornerCenters = {
            new Vector2(rect.x + cornerRadius, rect.y + cornerRadius), // Bottom-left
            new Vector2(rect.z - cornerRadius, rect.y + cornerRadius), // Bottom-right
            new Vector2(rect.z - cornerRadius, rect.w - cornerRadius), // Top-right
            new Vector2(rect.x + cornerRadius, rect.w - cornerRadius)  // Top-left
        };

        // Starting angles for each corner
        float[] startAngles = {
            Mathf.PI,        // Bottom-left: start from left (180°)
            3f * Mathf.PI / 2f, // Bottom-right: start from bottom (270°)
            0f,              // Top-right: start from right (0°)
            Mathf.PI / 2f    // Top-left: start from top (90°)
        };

        // Generate vertices for each corner
        for (int corner = 0; corner < 4; corner++)
        {
            float startAngle = startAngles[corner];

            // For very small corner radius, just add the corner center point
            if (cornerRadius < 0.5f)
            {
                vertices.Add(cornerCenters[corner]);
            }
            else
            {
                // Generate arc vertices
                for (int i = 0; i <= segments; i++)
                {
                    float angle = startAngle + (i * Mathf.PI * 0.5f / segments);
                    Vector2 offset = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle)) * cornerRadius;
                    vertices.Add(cornerCenters[corner] + offset);
                }
            }
        }

        return vertices;
    }


}

public enum StrokeLocation
{
    Center,
    Inside,
    Outside
}